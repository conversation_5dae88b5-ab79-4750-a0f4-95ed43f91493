import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ArrowRight, Download, Copy, CheckCircle, Sparkles } from 'lucide-react';
import { useDocument } from '@/contexts/DocumentContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import apiService from '@/services/api';
import Header from '@/components/Header';

const FeatureExtraction = () => {
  const navigate = useNavigate();
  const { uploadedFile, translatedContent, extractedFeatures, setExtractedFeatures } = useDocument();
  const [isExtracting, setIsExtracting] = useState(false);
  const [showFeatures, setShowFeatures] = useState(false);

  useEffect(() => {
    if (!uploadedFile || !translatedContent) {
      navigate('/');
      return;
    }
  }, [uploadedFile, translatedContent]);

  const handleFeatureExtraction = async () => {
    setIsExtracting(true);
    
    try {
      // Call the backend feature extraction API
      const response = await apiService.extractFeatures(uploadedFile?.name || '');
      
      // Store the markdown content directly
      setExtractedFeatures([response.markdown]);
      setShowFeatures(true);
      
      toast({
        title: "Features Extracted",
        description: "Successfully extracted features from the document.",
      });
    } catch (error) {
      console.error('Feature extraction error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to extract features";
      
      // Check if the error is about missing translation
      if (errorMessage.includes("Translated HTML file not found")) {
        toast({
          title: "Translation Required",
          description: "Please translate the PDF document first before extracting features.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Feature Extraction Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setIsExtracting(false);
    }
  };

  const handleCopyFeatures = () => {
    if (extractedFeatures && extractedFeatures[0]) {
      navigator.clipboard.writeText(extractedFeatures[0]);
      toast({
        title: "Copied to Clipboard",
        description: "Feature analysis has been copied to your clipboard.",
      });
    }
  };

  const handleExportFeatures = () => {
    if (extractedFeatures && extractedFeatures[0]) {
      const blob = new Blob([extractedFeatures[0]], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'feature-analysis.md';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Export Complete",
        description: "Feature analysis has been exported as markdown file.",
      });
    }
  };

  const handleProceedToScope = () => {
    navigate('/scope');
  };

  if (!uploadedFile || !translatedContent) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <Header title="Feature Extraction" showBackButton backTo="/translate">
        {extractedFeatures && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyFeatures}
              className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold ring-1 ring-cyan-400/30 hover:ring-emerald-400/50"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportFeatures}
              className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold ring-1 ring-cyan-400/30 hover:ring-emerald-400/50"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleFeatureExtraction}
              disabled={isExtracting}
              className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold ring-1 ring-cyan-400/30 hover:ring-emerald-400/50"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Re-extract
            </Button>
          </>
        )}
        <Button
          onClick={handleProceedToScope}
          disabled={!extractedFeatures}
          className="bg-gradient-to-r from-cyan-500 to-emerald-500 hover:from-cyan-600 hover:to-emerald-600"
        >
          <ArrowRight className="h-4 w-4 mr-2" />
          Extract Scope of Work
        </Button>
      </Header>

      {/* Content */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Document Preview */}
          <div className="lg:col-span-2">
            <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-6">
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Document Analysis</h2>
                
                <div className="bg-slate-900/50 rounded-lg border border-slate-700 h-[calc(100vh-240px)] overflow-hidden">
                  {!extractedFeatures && !isExtracting && (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center space-y-6 max-w-md">
                        <Sparkles className="h-16 w-16 text-cyan-400 mx-auto" />
                        <div className="space-y-4">
                          <h3 className="text-xl font-semibold text-white">Ready for Feature Extraction</h3>
                          <p className="text-slate-400">
                            Click the button below to analyze your document and extract key features using AI.
                          </p>
                          <Button
                            onClick={handleFeatureExtraction}
                            className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold"
                          >
                            <Sparkles className="h-5 w-5 mr-2" />
                            Extract Features
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {isExtracting && (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center space-y-6 max-w-md">
                        <div className="relative">
                          <div className="animate-spin rounded-full h-16 w-16 border-4 border-cyan-400 border-t-transparent mx-auto"></div>
                          <Sparkles className="h-8 w-8 text-cyan-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                        </div>
                        <div className="space-y-2">
                          <h3 className="text-xl font-semibold text-cyan-400">Analyzing Document</h3>
                          <p className="text-slate-400">AI is extracting key features and requirements...</p>
                        </div>
                        <div className="w-64 bg-slate-700 rounded-full h-2 mx-auto overflow-hidden">
                          <div className="bg-gradient-to-r from-cyan-500 to-emerald-500 h-full rounded-full animate-pulse w-3/4"></div>
                        </div>
                      </div>
                    </div>
                  )}

                  {translatedContent && !isExtracting && (
                    <div className="h-full flex flex-col">
                      {/* Re-extract button overlay */}
                      {extractedFeatures && (
                        <div className="p-4 border-b border-slate-700 bg-slate-800/30">
                          <Button
                            onClick={handleFeatureExtraction}
                            disabled={isExtracting}
                            size="sm"
                            className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold"
                          >
                            <Sparkles className="h-4 w-4 mr-2" />
                            Re-extract Features
                          </Button>
                        </div>
                      )}
                      
                      {/* Document content */}
                      <div 
                        className="bg-white text-black p-4 flex-1 overflow-auto"
                        style={{ 
                          fontFamily: 'Arial, sans-serif',
                          fontSize: '14px',
                          lineHeight: '1.6'
                        }}
                        dangerouslySetInnerHTML={{ 
                          __html: `
                            <style>
                              body { 
                                margin: 0; 
                                padding: 0; 
                                font-family: Arial, sans-serif; 
                                background: white;
                                color: #374151;
                              }
                              h1, h2, h3, h4, h5, h6 { 
                                color: #1f2937; 
                                margin-top: 1.5em; 
                                margin-bottom: 0.5em; 
                                font-weight: 600;
                              }
                              h1 { font-size: 1.5em; }
                              h2 { font-size: 1.3em; }
                              h3 { font-size: 1.1em; }
                              p { 
                                margin-bottom: 1em; 
                                color: #374151; 
                                line-height: 1.6;
                              }
                              ul, ol { 
                                margin-bottom: 1em; 
                                padding-left: 2em; 
                              }
                              li { 
                                margin-bottom: 0.5em; 
                                line-height: 1.5;
                              }
                              table { 
                                border-collapse: collapse; 
                                width: 100%; 
                                margin-bottom: 1em; 
                                font-size: 0.9em;
                              }
                              th, td { 
                                border: 1px solid #d1d5db; 
                                padding: 8px; 
                                text-align: left; 
                              }
                              th { 
                                background-color: #f3f4f6; 
                                font-weight: bold; 
                                color: #1f2937;
                              }
                              .highlight { 
                                background-color: #fef3c7; 
                                padding: 2px 4px; 
                                border-radius: 3px; 
                              }
                              blockquote {
                                border-left: 4px solid #3b82f6;
                                margin: 1em 0;
                                padding-left: 1em;
                                font-style: italic;
                                color: #6b7280;
                              }
                              code {
                                background-color: #f3f4f6;
                                padding: 2px 4px;
                                border-radius: 3px;
                                font-family: 'Courier New', monospace;
                                font-size: 0.9em;
                              }
                              pre {
                                background-color: #f3f4f6;
                                padding: 1em;
                                border-radius: 6px;
                                overflow-x: auto;
                                margin: 1em 0;
                              }
                              pre code {
                                background: none;
                                padding: 0;
                              }
                            </style>
                            ${translatedContent}
                          `
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>

          {/* Features Panel */}
          <div className={`transition-all duration-500 ${showFeatures ? 'animate-slide-in-left' : ''}`}>
            <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-white">Extracted Features</h2>
                  {extractedFeatures && (
                    <div className="flex items-center gap-2 text-emerald-400">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">Analysis Complete</span>
                    </div>
                  )}
                </div>
                
                <div className="bg-slate-900/50 rounded-lg border border-slate-700 h-[calc(100vh-240px)] overflow-hidden">
                  {extractedFeatures ? (
                    <div className="p-6 h-full overflow-y-auto prose prose-invert max-w-none text-white">
                      <div className="space-y-6">
                        {extractedFeatures[0].split('\n').map((line, index) => {
                          if (line.startsWith('# ')) {
                            return <h1 key={index} className="text-2xl font-bold text-white mb-4">{line.replace('# ', '')}</h1>;
                          } else if (line.startsWith('## ')) {
                            return <h2 key={index} className="text-xl font-semibold text-cyan-400 mt-6 mb-3">{line.replace('## ', '')}</h2>;
                          } else if (line.startsWith('### ')) {
                            return <h3 key={index} className="text-lg font-medium text-emerald-400 mt-4 mb-2">{line.replace('### ', '')}</h3>;
                          } else if (line.startsWith('- **')) {
                            const match = line.match(/- \*\*(.*?)\*\*(.*)/);
                            if (match) {
                              return (
                                <div key={index} className="flex items-start gap-3 ml-4 mb-2">
                                  <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                                  <p><span className="font-semibold text-cyan-300">{match[1]}</span>{match[2]}</p>
                                </div>
                              );
                            }
                            return <p key={index} className="ml-4">{line}</p>;
                          } else if (line.startsWith('- ')) {
                            return (
                              <div key={index} className="flex items-start gap-3 ml-4 mb-1">
                                <div className="w-1.5 h-1.5 bg-slate-400 rounded-full mt-2.5 flex-shrink-0"></div>
                                <p>{line.replace('- ', '')}</p>
                              </div>
                            );
                          } else if (line.trim() === '') {
                            return <div key={index} className="h-4"></div>;
                          } else {
                            return <p key={index} className="leading-relaxed" style={{color: 'white'}}>{line}</p>;
                          }
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-slate-400">
                      <p>Extracted features will appear here</p>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeatureExtraction;
