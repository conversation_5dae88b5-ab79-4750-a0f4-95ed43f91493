import React, { useState, useEffect, useRef } from 'react';

interface TypewriterTextProps {
  text: string;
  speed?: number; // Characters per second
  onComplete?: () => void;
  className?: string;
}

const TypewriterText: React.FC<TypewriterTextProps> = ({
  text,
  speed = 50, // Default 50 characters per second
  onComplete,
  className = ''
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Reset when text changes
    setDisplayedText('');
    setCurrentIndex(0);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (text.length === 0) return;

    const intervalDelay = 1000 / speed; // Convert speed to interval delay

    intervalRef.current = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        if (prevIndex >= text.length) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          onComplete?.();
          return prevIndex;
        }

        const nextIndex = prevIndex + 1;
        setDisplayedText(text.slice(0, nextIndex));
        return nextIndex;
      });
    }, intervalDelay);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [text, speed, onComplete]);

  return (
    <div className={className}>
      {displayedText}
      {currentIndex < text.length && (
        <span className="animate-pulse text-cyan-400">|</span>
      )}
    </div>
  );
};

export default TypewriterText;
