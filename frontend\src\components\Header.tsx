import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  backTo?: string;
  children?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({ 
  title, 
  showBackButton = false, 
  backTo = '/',
  children 
}) => {
  const navigate = useNavigate();

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-10">
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Logo */}
            <div className="flex items-center">
              <img 
                src="/f3.svg" 
                alt="F3 Logo" 
                className="h-8 w-auto mr-4"
                onClick={() => navigate('/')}
                style={{ cursor: 'pointer' }}
              />
            </div>
            
            {/* Back Button */}
            {showBackButton && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(backTo)}
                  className="text-slate-400 hover:text-white"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <div className="h-6 border-l border-slate-600"></div>
              </>
            )}
            
            {/* Title */}
            <h1 className="text-xl font-semibold text-white">{title}</h1>
          </div>
          
          {/* Right side content */}
          <div className="flex items-center gap-3">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header; 