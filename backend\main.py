from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
import os
import shutil
from pathlib import Path
import uvicorn
from typing import Optional
import json
import re
import asyncio
import queue
import threading

# Import our custom modules
from htmltranslator import translate_html_file
from sow import extract_scope_from_docx, extract_features_from_docx
import tempfile
import os
from plumber import extract_arabic_pdf_to_html

app = FastAPI(title="RFP Processing API", version="1.0.0")

# Configure CORS for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:8080", "http://127.0.0.1:5173", "http://************", "http://localhost:8081"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory if it doesn't exist
UPLOADS_DIR = Path("uploads")
UPLOADS_DIR.mkdir(exist_ok=True)

@app.get("/")
async def root():
    return {"message": "RFP Processing API is running"}

@app.post("/upload-pdf")
async def upload_pdf(file: UploadFile = File(...)):
    """
    Upload a PDF file for processing
    """
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
        # Clean the uploads directory
    for existing_file in UPLOADS_DIR.iterdir():
        try:
            if existing_file.is_file():
                existing_file.unlink()
        except Exception as e:
            print(f"[Warning] Could not delete file {existing_file}: {e}")
    
    # Save file to uploads directory
    file_path = UPLOADS_DIR / file.filename
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    return {
        "message": "File uploaded successfully",
        "filename": file.filename,
        "file_path": str(file_path)
    }

@app.post("/translate-pdf")
async def translate_pdf(filename: str = Query(...)):
    """
    Translate PDF to HTML using plumber and the HTML translator
    """
    print(f"[API] Translating PDF file: {filename}")
    file_path = UPLOADS_DIR / filename
    print(f"[API] Looking for PDF file at: {file_path}")
    print(f"[API] File exists: {file_path.exists()}")
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    try:
        # Step 1: Extract HTML from PDF using plumber
        plumber_html = UPLOADS_DIR / f"{filename.replace('.pdf', '_plumber.html')}"
        print(f"[API] Running plumber: extract_arabic_pdf_to_html({file_path}, {plumber_html})")
        page_count = extract_arabic_pdf_to_html(str(file_path), str(plumber_html))
        print(f"[API] Plumber HTML file created: {plumber_html}")
        # Step 2: Translate the HTML using htmltranslator
        output_html = UPLOADS_DIR / f"{filename.replace('.pdf', '_translated.html')}"
        print(f"[API] Running htmltranslator: translate_html_file({plumber_html}, {output_html})")
        translate_html_file(str(plumber_html), str(output_html), int(page_count))
        print(f"[API] Translation completed, reading HTML content from: {output_html}")
        with open(output_html, 'r', encoding='utf-8') as f:
            translated_content = f.read()
        print(f"[API] Successfully read translated content, length: {len(translated_content)} characters")
        return {
            "message": "Translation completed successfully",
            "translated_content": translated_content,
            "output_file": str(output_html)
        }
    except Exception as e:
        print(f"[API] Translation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")

@app.get("/translate-pdf-stream")
async def translate_pdf_stream(filename: str = Query(...)):
    """
    Stream PDF translation progress using Server-Sent Events
    """
    print(f"[API] Starting streaming translation for: {filename}")
    file_path = UPLOADS_DIR / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")

    async def generate_stream():
        try:
            # Step 1: Extract HTML from PDF using plumber
            plumber_html = UPLOADS_DIR / f"{filename.replace('.pdf', '_plumber.html')}"
            print(f"[API] Running plumber: extract_arabic_pdf_to_html({file_path}, {plumber_html})")

            # Send initial status
            yield f"data: {json.dumps({'type': 'status', 'message': 'Extracting content from PDF...', 'progress': 10})}\n\n"

            page_count = extract_arabic_pdf_to_html(str(file_path), str(plumber_html))
            print(f"[API] Plumber HTML file created: {plumber_html}")

            # Send extraction complete status
            yield f"data: {json.dumps({'type': 'status', 'message': 'Content extracted. Starting translation...', 'progress': 20})}\n\n"

            # Step 2: Translate the HTML using htmltranslator with streaming
            output_html = UPLOADS_DIR / f"{filename.replace('.pdf', '_translated.html')}"
            print(f"[API] Running htmltranslator with streaming: translate_html_file({plumber_html}, {output_html})")

            # Create a callback function to stream translation progress
            def stream_callback(batch_data):
                batch_number = batch_data['batch_number']
                total_batches = batch_data['total_batches']
                text = batch_data['text']

                # Calculate progress (20% for extraction, 70% for translation, 10% for final processing)
                translation_progress = int(20 + (batch_number / total_batches) * 70)

                stream_data = {
                    'type': 'translation_chunk',
                    'batch_number': batch_number,
                    'total_batches': total_batches,
                    'text': text,
                    'progress': translation_progress
                }

                # We need to use a different approach for streaming from sync callback
                # Store the data to be yielded
                stream_queue.put(stream_data)

            # Create a queue to handle streaming from sync callback
            stream_queue = queue.Queue()

            # Run translation in a separate thread
            def run_translation():
                try:
                    translate_html_file(str(plumber_html), str(output_html), int(page_count), stream_callback)
                    stream_queue.put({'type': 'translation_complete'})
                except Exception as e:
                    stream_queue.put({'type': 'error', 'message': str(e)})

            translation_thread = threading.Thread(target=run_translation)
            translation_thread.start()

            # Stream the results
            while translation_thread.is_alive() or not stream_queue.empty():
                try:
                    # Get data from queue with timeout
                    data = stream_queue.get(timeout=0.1)
                    yield f"data: {json.dumps(data)}\n\n"

                    if data.get('type') == 'translation_complete':
                        break
                    elif data.get('type') == 'error':
                        raise Exception(data.get('message', 'Translation failed'))

                except queue.Empty:
                    continue

            # Wait for thread to complete
            translation_thread.join()

            # Send final completion status
            yield f"data: {json.dumps({'type': 'status', 'message': 'Translation completed successfully!', 'progress': 100})}\n\n"

            # Read the final translated content
            with open(output_html, 'r', encoding='utf-8') as f:
                translated_content = f.read()

            # Send the final translated content
            yield f"data: {json.dumps({'type': 'final_content', 'content': translated_content})}\n\n"

        except Exception as e:
            print(f"[API] Streaming translation error: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
        }
    )

@app.get("/translate-pdf-stream-v2")
async def translate_pdf_stream_v2(filename: str = Query(...)):
    """
    Improved streaming translation endpoint with cross-platform compatibility
    This version uses asyncio properly and should work on macOS, Ubuntu, and Windows
    """
    print(f"[API] Starting improved streaming translation for: {filename}")
    file_path = UPLOADS_DIR / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")

    async def generate_stream():
        try:
            # Step 1: Extract HTML from PDF using plumber
            plumber_html = UPLOADS_DIR / f"{filename.replace('.pdf', '_plumber.html')}"
            print(f"[API] Running plumber: extract_arabic_pdf_to_html({file_path}, {plumber_html})")

            # Send initial status
            yield f"data: {json.dumps({'type': 'status', 'message': 'Extracting content from PDF...', 'progress': 10})}\n\n"

            # Run plumber in executor to avoid blocking
            loop = asyncio.get_event_loop()
            page_count = await loop.run_in_executor(None, extract_arabic_pdf_to_html, str(file_path), str(plumber_html))
            print(f"[API] Plumber HTML file created: {plumber_html}")

            # Send extraction complete status
            yield f"data: {json.dumps({'type': 'status', 'message': 'Content extracted. Starting translation...', 'progress': 20})}\n\n"

            # Step 2: Translate the HTML using htmltranslator with streaming
            output_html = UPLOADS_DIR / f"{filename.replace('.pdf', '_translated.html')}"
            print(f"[API] Running htmltranslator with streaming: translate_html_file({plumber_html}, {output_html})")

            # Create an asyncio queue for cross-platform compatibility
            stream_queue = asyncio.Queue()
            translation_complete = asyncio.Event()
            translation_error = None

            # Create a thread-safe queue for callback communication
            callback_queue = queue.Queue()

            # Create a callback function to stream translation progress
            def stream_callback(batch_data):
                batch_number = batch_data['batch_number']
                total_batches = batch_data['total_batches']
                text = batch_data['text']

                # Calculate progress (20% for extraction, 70% for translation, 10% for final processing)
                translation_progress = int(20 + (batch_number / total_batches) * 70)

                stream_data = {
                    'type': 'translation_chunk',
                    'batch_number': batch_number,
                    'total_batches': total_batches,
                    'text': text,
                    'progress': translation_progress
                }

                # Use synchronous queue to avoid interfering with translation process
                callback_queue.put(stream_data)

            # Run translation in a dedicated thread (like original) to preserve translation quality
            def run_translation():
                try:
                    translate_html_file(str(plumber_html), str(output_html), int(page_count), stream_callback)
                    callback_queue.put({'type': 'translation_complete'})
                except Exception as e:
                    callback_queue.put({'type': 'error', 'message': str(e)})

            # Start translation in a separate thread (preserves original behavior)
            translation_thread = threading.Thread(target=run_translation)
            translation_thread.start()

            # Stream the results with hybrid approach: thread for translation, async for streaming
            last_keepalive = asyncio.get_event_loop().time()
            keepalive_interval = 10  # Send keepalive every 10 seconds

            while translation_thread.is_alive() or not callback_queue.empty():
                try:
                    # Get data from callback queue with timeout
                    data = callback_queue.get(timeout=0.5)

                    # Transfer to async queue for streaming
                    await stream_queue.put(data)
                    yield f"data: {json.dumps(data)}\n\n"
                    last_keepalive = asyncio.get_event_loop().time()

                    if data.get('type') == 'translation_complete':
                        break
                    elif data.get('type') == 'error':
                        raise Exception(data.get('message', 'Translation error'))

                except queue.Empty:
                    # Send keepalive to prevent connection timeout on some platforms
                    current_time = asyncio.get_event_loop().time()
                    if current_time - last_keepalive > keepalive_interval:
                        yield f"data: {json.dumps({'type': 'keepalive'})}\n\n"
                        last_keepalive = current_time

                    # Small async sleep to prevent busy waiting
                    await asyncio.sleep(0.1)
                    continue

            # Wait for thread to complete
            translation_thread.join()

            # Process any remaining items in callback queue
            while not callback_queue.empty():
                try:
                    data = callback_queue.get(timeout=0.1)
                    await stream_queue.put(data)
                    yield f"data: {json.dumps(data)}\n\n"
                    if data.get('type') in ['translation_complete', 'error']:
                        break
                except queue.Empty:
                    break

            # Send final completion status
            yield f"data: {json.dumps({'type': 'status', 'message': 'Reading translated content...', 'progress': 90})}\n\n"

            # Read the final translated content
            with open(output_html, 'r', encoding='utf-8') as f:
                translated_content = f.read()

            print(f"[API] Successfully read translated content, length: {len(translated_content)} characters")

            # Send the final translated content
            yield f"data: {json.dumps({'type': 'final_content', 'content': translated_content})}\n\n"

        except Exception as e:
            print(f"[API] Streaming translation error: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
        }
    )

@app.get("/test-translation-quality")
async def test_translation_quality(filename: str = Query(...)):
    """
    Test endpoint to compare translation quality between original and improved streaming
    Returns both outputs for comparison
    """
    print(f"[TEST] Testing translation quality for: {filename}")
    file_path = UPLOADS_DIR / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")

    try:
        # Test 1: Original non-streaming translation (baseline)
        plumber_html_orig = UPLOADS_DIR / f"{filename.replace('.pdf', '_test_orig_plumber.html')}"
        output_html_orig = UPLOADS_DIR / f"{filename.replace('.pdf', '_test_orig_translated.html')}"

        page_count = extract_arabic_pdf_to_html(str(file_path), str(plumber_html_orig))
        translate_html_file(str(plumber_html_orig), str(output_html_orig), int(page_count))

        with open(output_html_orig, 'r', encoding='utf-8') as f:
            original_content = f.read()

        # Test 2: Original streaming translation
        plumber_html_stream = UPLOADS_DIR / f"{filename.replace('.pdf', '_test_stream_plumber.html')}"
        output_html_stream = UPLOADS_DIR / f"{filename.replace('.pdf', '_test_stream_translated.html')}"

        extract_arabic_pdf_to_html(str(file_path), str(plumber_html_stream))

        stream_results = []
        def test_stream_callback(batch_data):
            stream_results.append({
                'batch': batch_data['batch_number'],
                'total': batch_data['total_batches'],
                'text_length': len(batch_data['text'])
            })

        translate_html_file(str(plumber_html_stream), str(output_html_stream), int(page_count), test_stream_callback)

        with open(output_html_stream, 'r', encoding='utf-8') as f:
            streaming_content = f.read()

        # Compare results
        quality_comparison = {
            "original_length": len(original_content),
            "streaming_length": len(streaming_content),
            "length_difference": abs(len(original_content) - len(streaming_content)),
            "content_identical": original_content == streaming_content,
            "stream_batches_processed": len(stream_results),
            "stream_batch_info": stream_results
        }

        return {
            "message": "Translation quality test completed",
            "quality_comparison": quality_comparison,
            "original_sample": original_content[:500] + "..." if len(original_content) > 500 else original_content,
            "streaming_sample": streaming_content[:500] + "..." if len(streaming_content) > 500 else streaming_content
        }

    except Exception as e:
        print(f"[TEST] Translation quality test error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Quality test failed: {str(e)}")

@app.post("/analyze-scope")
async def analyze_scope(filename: str = Query(...)):
    """
    Extract scope of work from the translated HTML file using the existing sow functions
    """
    print(f"Analyzing scope for file: {filename}")
    
    # Look for the translated HTML file
    html_file_path = UPLOADS_DIR / f"{filename.replace('.pdf', '_translated.html')}"
    
    print(f"Looking for HTML file at: {html_file_path}")
    print(f"File exists: {html_file_path.exists()}")
    
    if not html_file_path.exists():
        raise HTTPException(status_code=404, detail="Translated HTML file not found. Please translate the PDF first.")
    
    try:
        # Read the translated HTML content
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"Successfully read HTML content, length: {len(html_content)} characters")
        
        # Extract text content from HTML
        from bs4 import BeautifulSoup
        
        # Parse HTML and extract text
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text content
        text_content = soup.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text_content.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text_content = ' '.join(chunk for chunk in chunks if chunk)
        
        print(f"Extracted text content length: {len(text_content)} characters")
        
        # Use the existing extract_scope_from_docx function logic
        scope_md = extract_scope_from_text_content(text_content)
        
        print("Returning scope analysis response")
        return {
            "message": "Scope of work extracted",
            "markdown": scope_md
        }
    except Exception as e:
        print(f"Error in scope analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def extract_scope_from_text_content(text_content: str) -> str:
    """
    Extract scope from text content using the existing sow logic
    """
    from sow import call_groq, chunk_text, extract_chunked, consolidate_with_llm
    
    # Use the same prompts and logic from sow.py
    scope_prompt = """You are a project manager analyzing an RFP. Extract ONLY the scope of work from the provided content. Focus on:

1. Project Overview
2. Core Deliverables
3. Technical Requirements
4. Timeline & Milestones
5. Resources Required
6. Quality Assurance
7. Success Criteria

Format your response as a clear, structured scope document."""

    final_scope_prompt = """You are a senior project manager and technical writer. Review and consolidate the scope analysis into a comprehensive, well-structured markdown document.

Organize the scope into clear sections:
- Project Overview
- Core Deliverables
- Technical Requirements
- Timeline & Milestones
- Resources Required
- Quality Assurance
- Success Criteria

Format as proper markdown with headers and bullet points."""

    # Process the text content using the same logic as extract_scope_from_docx
    chunks = chunk_text(text_content)
    raw_scope = extract_chunked(chunks, scope_prompt, "Scope of Work")
    final_scope = consolidate_with_llm(raw_scope, final_scope_prompt, "📦 Scope of Work")
    
    return final_scope



@app.post("/analyze-features")
async def analyze_features(filename: str = Query(...)):
    """
    Extract features from the translated HTML file using the existing sow functions
    """
    print(f"Analyzing features for file: {filename}")
    
    # Look for the translated HTML file
    html_file_path = UPLOADS_DIR / f"{filename.replace('.pdf', '_translated.html')}"
    
    print(f"Looking for HTML file at: {html_file_path}")
    print(f"File exists: {html_file_path.exists()}")
    
    if not html_file_path.exists():
        raise HTTPException(status_code=404, detail="Translated HTML file not found. Please translate the PDF first.")
    
    try:
        # Read the translated HTML content
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"Successfully read HTML content, length: {len(html_content)} characters")
        
        # Extract text content from HTML
        from bs4 import BeautifulSoup
        
        # Parse HTML and extract text
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text content
        text_content = soup.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text_content.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text_content = ' '.join(chunk for chunk in chunks if chunk)
        
        print(f"Extracted text content length: {len(text_content)} characters")
        
        # Create a temporary file with the extracted text content
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(text_content)
            temp_file_path = temp_file.name
        
        try:
            # Use the existing extract_features_from_docx function
            # We'll create a simple wrapper that works with text content
            features_md = extract_features_from_text_content(text_content)
            
            print("Returning feature extraction response")
            return {
                "message": "Feature set extracted",
                "markdown": features_md
            }
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except Exception as e:
        print(f"Error in feature extraction: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def extract_features_from_text_content(text_content: str) -> str:
    """
    Extract features from text content using the existing sow logic
    """
    from sow import call_groq, chunk_text, extract_chunked, consolidate_with_llm
    
    # Use the same prompts and logic from sow.py
    feature_prompt = """You are a cloud architect reviewing an RFP. Extract ONLY the technical Feature Set from the provided content. Focus on:

1. Technical Requirements
2. Functional Requirements  
3. Infrastructure Requirements
4. Security Requirements
5. Performance Requirements
6. Integration Requirements

Format your response as a clear, structured list of features. Be specific and technical."""

    final_feature_prompt = """You are a solution architect and technical editor. Review and consolidate the feature analysis into a comprehensive, well-structured markdown document.

Organize the features into clear categories:
- Technical Requirements
- Functional Requirements
- Infrastructure Requirements
- Security Requirements
- Performance Requirements
- Integration Requirements

Format as proper markdown with headers and bullet points."""

    # Process the text content using the same logic as extract_features_from_docx
    chunks = chunk_text(text_content)
    raw_features = extract_chunked(chunks, feature_prompt, "Feature Set")
    final_features = consolidate_with_llm(raw_features, final_feature_prompt, "🧩 Feature Set")
    
    return final_features



@app.get("/files")
async def list_files():
    """
    List all uploaded files
    """
    files = []
    for file_path in UPLOADS_DIR.glob("*.pdf"):
        files.append({
            "filename": file_path.name,
            "size": file_path.stat().st_size,
            "uploaded_at": file_path.stat().st_mtime
        })
    
    return {"files": files}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)