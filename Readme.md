# RFP-PARSER

## Overview

**RFP-PARSER** is a specialized tool by Folio3 for extracting and translating Arabic-language RFPs (Request for Proposals) from PDF documents. It provides a complete pipeline to:

- Convert Arabic PDFs into clean, structured HTML with correct right-to-left (RTL) rendering.
- Translate the extracted Arabic content into English using a state-of-the-art large language model (LLM) via HuggingFace.
- Offer a modern web interface for uploading, viewing, and managing documents.

The project consists of a Python backend for document processing and a React/TypeScript frontend for user interaction.

---

## Features

- **Arabic PDF to HTML Conversion:** Handles RTL layout, removes page numbers/footers, and preserves tables across pages.
- **Automated Translation:** Uses the `core42/jais-13b-chat` LLM for high-quality Arabic-to-English translation.
- **Web Interface:** Upload, view, and manage documents through a modern React frontend.
- **Preserves HTML Structure:** Only visible Arabic text is translated; HTML tags and layout remain intact.
- **GPU/CPU Support:** Translation pipeline can leverage GPU for performance.
- **Extensible UI Components:** Built with shadcn-ui and <PERSON>di<PERSON> for reusable, accessible components.

---

## Directory Structure

```
rfp-parser/
├── backend/
│   ├── main.py                # Main backend API (FastAPI)
│   ├── sow.py                 # Statement of Work extraction/processing
│   ├── htmltranslator.py      # HTML translation logic
│   ├── requirements.txt       # Python backend dependencies
│   └── uploads/               # Uploaded and processed files (PDFs, HTML)
├── frontend/
│   ├── src/
│   │   ├── App.tsx            # Main React app entry
│   │   ├── pages/             # Main application pages (FeatureExtraction, ScopeExtraction, etc.)
│   │   ├── components/        # UI components (shadcn-ui, Radix UI)
│   │   ├── services/          # API service layer
│   │   ├── contexts/          # React context providers
│   │   └── ...                # Hooks, utils, styles
│   ├── public/                # Static assets (favicon, pdf.worker, etc.)
│   ├── package.json           # Frontend dependencies and scripts
│   ├── tailwind.config.ts     # Tailwind CSS configuration
│   └── ...                    # Vite, TypeScript, ESLint configs
├── Readme.md                  # Project documentation (this file)
└── .gitignore                 # Git ignore rules
```

---

## Installation

### Backend (Python)

1. **Clone the repository:**
   ```sh
   git clone https://github.com/folio3-ai/RFP-PARSER.git
   cd rfp-parser
   ```

2. **Create and activate a virtual environment:**
   ```sh
   python -m venv venv
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install backend dependencies:**
   ```sh
   pip install -r backend/requirements.txt
   ```

### Frontend (React/TypeScript)

1. **Navigate to the frontend directory:**
   ```sh
   cd frontend
   ```

2. **Install frontend dependencies:**
   ```sh
   npm install
   # or
   bun install
   ```

---

## Usage

### Backend

- **Start the FastAPI server:**
  ```sh
  uvicorn backend.main:app --reload
  ```
- The backend will serve API endpoints for document upload, processing, and translation.

### Frontend

- **Start the development server:**
  ```sh
  npm run dev
  ```
- Open your browser to the provided local URL (typically `http://localhost:5173`).

### Document Processing Workflow

1. **Upload an Arabic PDF** via the web interface or place it in `backend/uploads/`.
2. **Conversion:** The backend processes the PDF, extracting structured HTML with RTL support.
3. **Translation:** The HTML is translated using the LLM, preserving all tags and layout.
4. **View/Download:** Access the translated HTML via the frontend or directly from `backend/uploads/`.

---

## Configuration

- **Environment Variables:** Not specified. (Check `backend/main.py` and related files for any required configuration.)
- **Frontend Configuration:** Vite, Tailwind, and TypeScript settings are in the `frontend/` directory.

---

## Dependencies

### Backend

- `fastapi`
- `uvicorn`
- `python-multipart`
- `mistralai`
- `groq`
- `python-docx`
- `tqdm`
- `beautifulsoup4`

(See `backend/requirements.txt` for exact versions.)

### Frontend

- React, TypeScript, Vite
- shadcn-ui, Radix UI, Tailwind CSS
- `@tanstack/react-query`, `react-router-dom`, `react-pdf`, `zod`, and more

(See `frontend/package.json` for full list.)

---

## Testing

- **Backend:** Not specified. (No explicit test scripts or frameworks found.)
- **Frontend:** Not specified. (No explicit test scripts or frameworks found.)

---

## Contributing

Not specified. Please open issues or pull requests for discussion before contributing.

---

## License

This project is licensed under the **MIT License**.

---

## Author

- Folio3 (https://folio3.ai)
- [Contributors not specified]

---

**If any section is marked "Not specified," please refer to the codebase or contact the maintainers for further details.**
