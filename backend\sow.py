import os
from docx import Document
from groq import Groq
from tqdm import tqdm
from textwrap import dedent

# === CONFIGURATION ===
API_KEY = "********************************************************"
MODEL = "llama-3.3-70b-versatile"
CHUNK_SIZE = 3000

# === PROMPTS ===
scope_prompt = dedent("""
You are a senior project manager analyzing an RFP. Your task is to extract ONLY the Scope of Work and reformat it strictly to match the following template structure:

=Product Scope Document: <PROJECT NAME>

---

1. Project Overview
- <overview of the project in clear professional prose>

---

2. Product Objectives
- <bullet points describing the main goals and outcomes the project should achieve>

---

3. MVP Scope

# Functionality:
- <bullet points listing core functional requirements>

# Supported File Types for Input:
- <bullet points listing file types, if applicable>

# User Interaction:
- <bullet points describing user-facing interactions>

# Users:
- <who will use the system>

# Translation Quality Requirements:
- <any quality requirements or constraints>

---

4. Post-MVP Scope (Future Phases)
- <detailed bullet points for future features or phases>

---

5. Technical Notes
- <technical constraints, expectations, and requirements>

---

6. Assumptions & Constraints
- <bullet points listing any known assumptions or limitations>

---

7. Success Criteria for MVP
- <clear, measurable criteria for delivery acceptance>

---

**Instructions for you (the LLM):**
- Map any relevant details in the provided text to these sections.
- If no content exists for a section, leave it blank or write "Not mentioned".
- Do not add content that is not present in the text.
- Use clear, concise, professional language.
- Preserve the heading structure exactly as shown.
""")

feature_prompt = dedent("""
You are a cloud architect reviewing an RFP. Your task is to extract ONLY the **technical and functional feature set** described in the text. Focus purely on **what the system will do** and **how it will behave** from a technical perspective.

Organize the extracted features in the following structured format:

=Feature Set Document: <PROJECT NAME>

---

1. Core Functional Features
- <bullet points describing the primary system capabilities and behaviors>
- e.g., "Translate Arabic text to English with semantic accuracy"

---

2. User Interface Features
- <bullet points focused on user-facing interactions>
- e.g., "Allow users to upload files through a web interface"

---

3. File Handling Features
- <what types of files the system supports, how they're processed>
- e.g., "Support input from PDF, DOCX, and TXT formats"

---

4. Output & Formatting Features
- <details on output formats and how formatting is preserved>
- e.g., "Preserve heading levels, bullet lists, tables, and font styling"

---

5. Advanced/AI Capabilities (Optional)
- <any smart, AI/ML-enabled behaviors if mentioned>
- e.g., "Detect missing requirements using NLP", "Auto-generate SOW drafts"

---

6. Integration & Extensibility
- <if mentioned, include APIs, export formats, or plug-in architecture>
- e.g., "Export final output to Google Docs", "Pluggable translation engine"

---

**Instructions for you (the LLM):**
- Only extract actual features; do not summarize business goals, background info, or strategy.
- Be concise and technical.
- Keep original intent and language where possible.
- If a section has no content, leave it blank or say "Not mentioned".
- NEVER invent or hallucinate features not present in the input.
""")

final_scope_prompt = dedent("""
You are a senior project manager and technical writer. Your task is to consolidate and polish the extracted Scope of Work text to strictly match the following structured template:

=Product Scope Document: <PROJECT NAME>

---

1. Project Overview
- <overview of the project>

---

2. Product Objectives
- <bullet points>

---

3. MVP Scope

# Functionality:
- <bullet points>

# Supported File Types for Input:
- <bullet points>

# User Interaction:
- <bullet points>

# Users:
- <bullet points>

# Translation Quality Requirements:
- <bullet points>

---

4. Post-MVP Scope (Future Phases)
- <bullet points or sub-lists>

---

5. Technical Notes
- <bullet points>

---

6. Assumptions & Constraints
- <bullet points>

---

7. Success Criteria for MVP
- <bullet points>

---

**Instructions for you (the LLM):**
- Produce a fully cleaned, well-formatted Scope of Work in this exact structure.
- Use professional, precise wording.
- Fill in sections with the content provided in the input while maintaining the template format.
- If a section has no content, leave it blank or write "Not mentioned".
- Do not invent any details or add content beyond what was provided.
""")

final_feature_prompt = dedent("""
You are a solution architect and technical editor. Your task is to take the extracted content and organize it into a polished, structured **Feature Set Document** using the following exact template:

=Feature Set Document: <PROJECT NAME>

---

1. Core Functional Features
- <bullet points>

---

2. User Interface Features
- <bullet points>

---

3. File Handling Features
- <bullet points>

---

4. Output & Formatting Features
- <bullet points>

---

5. Advanced/AI Capabilities (Optional)
- <bullet points>

---

6. Integration & Extensibility
- <bullet points>

---

**Instructions for you (the LLM):**
- Organize and rewrite the raw feature list into clearly written, professional points under each section.
- Be clear, technical, and concise.
- Do not modify the structure.
- If any section is empty, write “Not mentioned” or leave it blank.
- Do not fabricate or infer features beyond what is provided in the input.
""")


# === UTILITIES ===
def load_docx_text(path):
    doc = Document(path)
    return "\n".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])

def chunk_text(text, chunk_size=CHUNK_SIZE):
    paragraphs = text.split("\n")
    chunks, current = [], ""
    for para in paragraphs:
        if len(current) + len(para) < chunk_size:
            current += para + "\n"
        else:
            chunks.append(current.strip())
            current = para + "\n"
    if current.strip():
        chunks.append(current.strip())
    return chunks

def call_groq(prompt, content):
    client = Groq(api_key=API_KEY)
    try:
        response = client.chat.completions.create(
            model=MODEL,
            messages=[{"role": "user", "content": f"{prompt}\n\n{content}"}]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        return f"[ERROR] {e}"

def extract_chunked(chunks, prompt, label=""):
    results = []
    for i, chunk in enumerate(tqdm(chunks, desc=f"🔍 Extracting {label}")):
        result = call_groq(prompt, chunk)
        results.append(result)
    return "\n\n".join(results)

def consolidate_with_llm(raw_text, final_prompt, section_name=""):
    print(f"🧠 Final LLM pass for {section_name}...")
    return call_groq(final_prompt, raw_text)

# === EXPOSED FUNCTIONS ===
def extract_scope_from_docx(path: str) -> str:
    if not os.path.exists(path):
        raise FileNotFoundError(f"File not found: {path}")
    
    text = load_docx_text(path)
    chunks = chunk_text(text)
    raw_scope = extract_chunked(chunks, scope_prompt, "Scope of Work")
    final_scope = consolidate_with_llm(raw_scope, final_scope_prompt, "📦 Scope of Work")
    return final_scope

def extract_features_from_docx(path: str) -> str:
    if not os.path.exists(path):
        raise FileNotFoundError(f"File not found: {path}")
    
    text = load_docx_text(path)
    chunks = chunk_text(text)
    raw_features = extract_chunked(chunks, feature_prompt, "Feature Set")
    final_features = consolidate_with_llm(raw_features, final_feature_prompt, "🧩 Feature Set")
    return final_features
