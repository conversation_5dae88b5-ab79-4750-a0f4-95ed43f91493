import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Download, Copy, CheckCircle, FileText, Sparkles } from 'lucide-react';
import { useDocument } from '@/contexts/DocumentContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import apiService from '@/services/api';
import Header from '@/components/Header';

const ScopeExtraction = () => {
  const navigate = useNavigate();
  const { uploadedFile, extractedFeatures, scopeOfWork, setScopeOfWork, translatedContent } = useDocument();
  const [isExtracting, setIsExtracting] = useState(false);
  const [showScope, setShowScope] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!uploadedFile || !extractedFeatures) {
      navigate('/');
      return;
    }
  }, [uploadedFile, extractedFeatures]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isExtracting) {
      setProgress(0);
      interval = setInterval(() => {
        setProgress(prev => {
          // Increment by 2-6% randomly, but never above 95% while extracting
          if (prev < 95) {
            return Math.min(prev + Math.floor(Math.random() * 5) + 2, 95);
          }
          return prev;
        });
      }, 200);
    } else {
      // When extraction finishes, fill to 100% and reset after a short delay
      setProgress(100);
      setTimeout(() => setProgress(0), 500);
      if (interval) clearInterval(interval);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isExtracting]);

  const handleScopeExtraction = async () => {
    setIsExtracting(true);
    
    try {
      // Call the backend scope extraction API
      const response = await apiService.extractScope(uploadedFile?.name || '');
      setScopeOfWork(response.markdown);
      setShowScope(true);
      
      toast({
        title: "Scope of Work Extracted",
        description: "Successfully generated structured scope of work document.",
      });
    } catch (error) {
      console.error('Scope extraction error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to extract scope of work";
      
      // Check if the error is about missing translation
      if (errorMessage.includes("Translated HTML file not found")) {
        toast({
          title: "Translation Required",
          description: "Please translate the PDF document first before extracting scope of work.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Scope Extraction Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setIsExtracting(false);
    }
  };

  const handleCopyScope = () => {
    if (scopeOfWork) {
      navigator.clipboard.writeText(scopeOfWork);
      toast({
        title: "Copied to Clipboard",
        description: "Scope of work has been copied to your clipboard.",
      });
    }
  };

  const handleExportScope = () => {
    if (scopeOfWork) {
      const blob = new Blob([scopeOfWork], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'scope-of-work.md';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Export Complete",
        description: "Scope of work has been exported as Markdown file.",
      });
    }
  };

  if (!uploadedFile || !extractedFeatures) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <Header title="Scope of Work" showBackButton backTo="/features">
        {scopeOfWork && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyScope}
              className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold ring-1 ring-cyan-400/30 hover:ring-emerald-400/50"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportScope}
              className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold ring-1 ring-cyan-400/30 hover:ring-emerald-400/50"
            >
              <Download className="h-4 w-4 mr-2" />
              Export (.md)
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleScopeExtraction}
              disabled={isExtracting}
              className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold ring-1 ring-cyan-400/30 hover:ring-emerald-400/50"
            >
              <FileText className="h-4 w-4 mr-2" />
              Re-analyze
            </Button>
          </>
        )}
      </Header>

      {/* Content */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Control Panel */}
          <div className="lg:col-span-1">
            <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-6">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Analysis Summary</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-slate-900/50 rounded-lg">
                      <span className="text-slate-400 text-sm">Features Identified</span>
                      <span className="text-cyan-400 font-semibold">{extractedFeatures.length}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-slate-900/50 rounded-lg">
                      <span className="text-slate-400 text-sm">Document Status</span>
                      <span className="text-emerald-400 font-semibold">Analyzed</span>
                    </div>
                  </div>
                </div>

                {!scopeOfWork && !isExtracting && (
                  <div className="space-y-4">
                    <h4 className="text-white font-medium">Ready to Extract</h4>
                    <p className="text-slate-400 text-sm">
                      Generate a comprehensive scope of work document based on the extracted features.
                    </p>
                    <Button
                      onClick={handleScopeExtraction}
                      className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Extract Scope of Work
                    </Button>
                  </div>
                )}

                {scopeOfWork && (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-emerald-400">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">Scope Generated</span>
                    </div>
                    <p className="text-slate-400 text-sm">
                      Your scope of work document is ready for review and export.
                    </p>
                    <Button
                      onClick={handleScopeExtraction}
                      disabled={isExtracting}
                      className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Re-analyze Scope
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Document Viewer */}
          <div className="lg:col-span-1">
            <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-6">
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Translated Document</h2>
                
                <div className="bg-slate-900/50 rounded-lg border border-slate-700 h-[calc(100vh-240px)] overflow-hidden">
                  {translatedContent ? (
                    <div 
                      className="bg-white text-black p-4 h-full overflow-auto"
                      style={{ 
                        fontFamily: 'Arial, sans-serif',
                        fontSize: '14px',
                        lineHeight: '1.6'
                      }}
                      dangerouslySetInnerHTML={{ 
                        __html: `
                          <style>
                            body { 
                              margin: 0; 
                              padding: 0; 
                              font-family: Arial, sans-serif; 
                              background: white;
                              color: #374151;
                            }
                            h1, h2, h3, h4, h5, h6 { 
                              color: #1f2937; 
                              margin-top: 1.5em; 
                              margin-bottom: 0.5em; 
                              font-weight: 600;
                            }
                            h1 { font-size: 1.5em; }
                            h2 { font-size: 1.3em; }
                            h3 { font-size: 1.1em; }
                            p { 
                              margin-bottom: 1em; 
                              color: #374151; 
                              line-height: 1.6;
                            }
                            ul, ol { 
                              margin-bottom: 1em; 
                              padding-left: 2em; 
                            }
                            li { 
                              margin-bottom: 0.5em; 
                              line-height: 1.5;
                            }
                            table { 
                              border-collapse: collapse; 
                              width: 100%; 
                              margin-bottom: 1em; 
                              font-size: 0.9em;
                            }
                            th, td { 
                              border: 1px solid #d1d5db; 
                              padding: 8px; 
                              text-align: left; 
                            }
                            th { 
                              background-color: #f3f4f6; 
                              font-weight: bold; 
                              color: #1f2937;
                            }
                            .highlight { 
                              background-color: #fef3c7; 
                              padding: 2px 4px; 
                              border-radius: 3px; 
                            }
                            blockquote {
                              border-left: 4px solid #3b82f6;
                              margin: 1em 0;
                              padding-left: 1em;
                              font-style: italic;
                              color: #6b7280;
                            }
                            code {
                              background-color: #f3f4f6;
                              padding: 2px 4px;
                              border-radius: 3px;
                              font-family: 'Courier New', monospace;
                              font-size: 0.9em;
                            }
                            pre {
                              background-color: #f3f4f6;
                              padding: 1em;
                              border-radius: 6px;
                              overflow-x: auto;
                              margin: 1em 0;
                            }
                            pre code {
                              background: none;
                              padding: 0;
                            }
                          </style>
                          ${translatedContent}
                        `
                      }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full text-slate-400">
                      <p>Translated document will appear here</p>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>

          {/* Scope Document */}
          <div className="lg:col-span-1">
            <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-6">
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Scope of Work Document</h2>
                
                <div className="bg-slate-900/50 rounded-lg border border-slate-700 h-[calc(100vh-240px)] overflow-hidden">
                  {isExtracting ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center space-y-6">
                        <div className="relative">
                          <div className="animate-spin rounded-full h-16 w-16 border-4 border-cyan-400 border-t-transparent mx-auto"></div>
                          <Sparkles className="h-8 w-8 text-cyan-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                        </div>
                        <div className="space-y-2">
                          <h3 className="text-xl font-semibold text-cyan-400">Generating Scope of Work</h3>
                          <p className="text-slate-400">AI is structuring your project scope...</p>
                        </div>
                        <div className="w-64 bg-slate-700 rounded-full h-2 mx-auto overflow-hidden">
                          <div
                            className="bg-gradient-to-r from-cyan-500 to-emerald-500 h-full rounded-full transition-all duration-200"
                            style={{ width: `${progress}%` }}
                          ></div>
                        </div>
                        <div className="text-cyan-300 text-xs font-mono">{progress}%</div>
                      </div>
                    </div>
                  ) : scopeOfWork ? (
                    <div className={`p-6 h-full overflow-y-auto prose prose-invert max-w-none text-white ${showScope ? 'animate-fade-in' : ''}`}>
                      <div className="space-y-6">
                        {scopeOfWork.split('\n').map((line, index) => {
                          if (line.startsWith('# ')) {
                            return <h1 key={index} className="text-2xl font-bold text-white mb-4">{line.replace('# ', '')}</h1>;
                          } else if (line.startsWith('## ')) {
                            return <h2 key={index} className="text-xl font-semibold text-cyan-400 mt-6 mb-3">{line.replace('## ', '')}</h2>;
                          } else if (line.startsWith('### ')) {
                            return <h3 key={index} className="text-lg font-medium text-emerald-400 mt-4 mb-2">{line.replace('### ', '')}</h3>;
                          } else if (line.startsWith('- **')) {
                            const match = line.match(/- \*\*(.*?)\*\*(.*)/);
                            if (match) {
                              return (
                                <div key={index} className="flex items-start gap-3 ml-4 mb-2">
                                  <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                                  <p><span className="font-semibold text-cyan-300">{match[1]}</span>{match[2]}</p>
                                </div>
                              );
                            }
                            return <p key={index} className="ml-4">{line}</p>;
                          } else if (line.startsWith('- ')) {
                            return (
                              <div key={index} className="flex items-start gap-3 ml-4 mb-1">
                                <div className="w-1.5 h-1.5 bg-slate-400 rounded-full mt-2.5 flex-shrink-0"></div>
                                <p>{line.replace('- ', '')}</p>
                              </div>
                            );
                          } else if (line.trim() === '') {
                            return <div key={index} className="h-4"></div>;
                          } else {
                            return <p key={index} className="leading-relaxed" style={{color: 'white'}}>{line}</p>;
                          }
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-slate-400">
                      <div className="text-center space-y-4">
                        <FileText className="h-16 w-16 mx-auto opacity-50" />
                        <p>Scope of work document will appear here</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScopeExtraction;
