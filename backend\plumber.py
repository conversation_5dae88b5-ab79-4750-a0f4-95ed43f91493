from google import genai
from google.genai import types
import pathlib
from dotenv import load_dotenv
import os
from markdown_it import MarkdownIt
import pdfplumber

# === LOAD ENV & INIT CLIENT ===
load_dotenv()
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    raise ValueError("GEMINI_API_KEY environment variable is required but not found")
client = genai.Client(api_key=api_key)

# === MAIN FUNCTION ===
def extract_arabic_pdf_to_html(pdf_path, output_html_path, output_md_path=None):
    pdf_path = pathlib.Path(pdf_path)
    output_html_path = pathlib.Path(output_html_path)
    if output_md_path:
        output_md_path = pathlib.Path(output_md_path)

    # === GET PAGE COUNT ===
    try:
        with pdfplumber.open(pdf_path) as pdf:
            page_count = len(pdf.pages)
    except Exception as e:
        print(f"⚠️ Failed to read page count: {e}")
        page_count = -1

    # === GENERATE MARKDOWN FROM PDF ===
    prompt = """
You are an expert document parser trained to extract Arabic content from PDFs and convert it into accurate, well-structured Markdown.

Your task:

- Ignore any recurring headers and footers that appear on every page. Focus only on the actual content.

1. Extract all Arabic text exactly as it appears in the document. **DO NOT hallucinate, interpret, or summarize.**
2. Preserve the document’s visual and logical structure, including:
   - Headings
   - Paragraphs
   - Ordered (numbered) lists
   - Unordered (bullet point) lists
   - Tables
3. Maintain the correct sequence and logical hierarchy as it appears in the original.

### Formatting Rules:

- Use `#`, `##`, `###`, etc., for headings and subheadings.
- Use plain paragraphs for regular body text.
- Use `-` or `*` for unordered list items.
- Use `1.`, `2.`, etc., for ordered lists.
- Use Markdown tables for tabular data, with proper headers and column alignment.

### Chain-of-Thought Extraction Strategy:

Before writing output:
- First identify the logical blocks of the document: headings, paragraphs, lists, and tables.
- Categorize each block into: `heading`, `paragraph`, `list`, or `table`.
- For each block, determine the correct Markdown formatting.
- Output each block in Markdown in the **same order as it appears**.

### Few-Shot Examples:

**Example 1 — Heading and Paragraph**  
Arabic Input:
```
الإدارة العامة لتقنية المعلومات
هذا القسم يتحدث عن أهمية البنية التحتية التقنية.
```

Markdown Output:
```
## الإدارة العامة لتقنية المعلومات

هذا القسم يتحدث عن أهمية البنية التحتية التقنية.
```

**Example 2 — Ordered List**  
Arabic Input:
```
1. تقديم الخدمات التقنية
2. تطوير الأنظمة
3. دعم المستخدمين
```

Markdown Output:
```
1. تقديم الخدمات التقنية  
2. تطوير الأنظمة  
3. دعم المستخدمين
```

**Example 3 — Table**  
Arabic Input:
```
اسم النشاط | الرمز | التصنيف
استضافة المواقع | 101 | A
تطوير البرمجيات | 102 | B
```

Markdown Output:
```
| اسم النشاط        | الرمز | التصنيف |
|-------------------|-------|----------|
| استضافة المواقع   | 101   | A        |
| تطوير البرمجيات   | 102   | B        |
```

### Final Instructions:

- Do not omit any text.
- Do not summarize or paraphrase.
- Do not add any extra words or interpretations.
- Only use Markdown to preserve structure exactly as seen in the input.

Begin parsing the document now.
"""

    response = client.models.generate_content(
        model="gemini-2.5-flash",
        contents=[
            types.Part.from_bytes(
                data=pdf_path.read_bytes(),
                mime_type='application/pdf',
            ),
            prompt
        ]
    )

    markdown_text = response.text

    # === SAVE MARKDOWN FILE IF NEEDED ===
    if output_md_path:
        with open(output_md_path, "w", encoding="utf-8") as f:
            f.write(markdown_text)

    # === CONVERT MARKDOWN TO HTML ===
    md = MarkdownIt("commonmark").enable("table").enable("fence")
    html_content = md.render(markdown_text)

    # === WRAP IN RTL HTML TEMPLATE ===
    rtl_html = f"""
    <!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
    <meta charset='UTF-8'>
    <title>Translated Arabic PDF</title>
    <style>
    body {{ font-family: Tahoma, sans-serif; font-size: 16px; direction: ltr; padding: 40px; max-width: 1000px; margin: auto; }}
    table {{ width: 100%; border-collapse: collapse; margin: 24px 0; background: #fff; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
    th, td {{ border: 1px solid #ccc; padding: 12px; text-align: left; }}
    th {{ background: #003366; color: white; }}
    tr:nth-child(even) {{ background-color: #f2f2f2; }}
    </style>
    </head>
    <body>
    {html_content}
    </body>
    </html>
    """

    # === WRITE TO HTML FILE ===
    with open(output_html_path, 'w', encoding='utf-8') as f:
        f.write(rtl_html)

    print(f"✅ HTML saved to: {output_html_path}")
    if output_md_path:
        print(f"📝 Markdown saved to: {output_md_path}")

    return page_count


# === CLI ENTRY ===
if __name__ == "__main__":
    import sys
    if len(sys.argv) < 3:
        print("Usage: python gemini_extractor.py <input.pdf> <output.html> [output.md]")
    else:
        pdf_path = sys.argv[1]
        output_html = sys.argv[2]
        output_md = sys.argv[3] if len(sys.argv) > 3 else None
        extract_arabic_pdf_to_html(pdf_path, output_html, output_md)
    
