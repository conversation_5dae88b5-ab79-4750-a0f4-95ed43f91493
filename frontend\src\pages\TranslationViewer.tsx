
import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ArrowRight, RotateCcw, Globe, Sparkles, ZoomIn, ZoomOut, Download, Copy } from 'lucide-react';
import { Document, Page, pdfjs } from 'react-pdf';
import { useDocument } from '@/contexts/DocumentContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import apiService from '@/services/api';
import Header from '@/components/Header';
import StreamingTranslationViewer from '@/components/StreamingTranslationViewer';

// Set up PDF.js worker - using local version
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

const TranslationViewer = () => {
  const navigate = useNavigate();
  const { uploadedFile, translatedContent, setTranslatedContent } = useDocument();
  const [isTranslating, setIsTranslating] = useState(false);
  const [numPages, setNumPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const translatedFileRef = useRef<string | null>(null);

  // Streaming state
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingData, setStreamingData] = useState<any>(null);
  const [accumulatedText, setAccumulatedText] = useState('');
  const [currentEventSource, setCurrentEventSource] = useState<EventSource | null>(null);

  useEffect(() => {
    if (!uploadedFile) {
      navigate('/');
      return;
    }

    // Only auto-start translation if this file hasn't been translated yet
    if (translatedFileRef.current !== uploadedFile.name && !translatedContent) {
      handleTranslation();
    }
  }, [uploadedFile, translatedContent]);

  // Cleanup effect for EventSource
  useEffect(() => {
    return () => {
      if (currentEventSource) {
        currentEventSource.close();
      }
    };
  }, [currentEventSource]);

  const handleTranslation = async () => {
    setIsTranslating(true);
    setIsStreaming(true);
    setAccumulatedText('');
    setStreamingData(null);

    try {
      // Close any existing EventSource
      if (currentEventSource) {
        currentEventSource.close();
      }

      const eventSource = apiService.translatePdfStream(
        uploadedFile?.name || '',
        // onProgress callback
        (data) => {
          setStreamingData(data);

          if (data.type === 'translation_chunk' && data.text) {
            // Add the new text to accumulated text after typewriter effect completes
            // This will be handled by the StreamingTranslationViewer component
          }
        },
        // onComplete callback
        (finalContent) => {
          setTranslatedContent(finalContent);
          translatedFileRef.current = uploadedFile?.name || null;
          setIsStreaming(false);
          setIsTranslating(false);

          toast({
            title: "Translation Complete",
            description: "Your document has been successfully translated.",
          });
        },
        // onError callback
        (error) => {
          console.error('Streaming translation error:', error);
          setIsStreaming(false);
          setIsTranslating(false);

          toast({
            title: "Translation Failed",
            description: error,
            variant: "destructive",
          });
        }
      );

      setCurrentEventSource(eventSource);

    } catch (error) {
      console.error('Translation error:', error);
      setIsStreaming(false);
      setIsTranslating(false);

      toast({
        title: "Translation Failed",
        description: error instanceof Error ? error.message : "Failed to translate document",
        variant: "destructive",
      });
    }
  };

  const handleStreamingComplete = () => {
    // When a batch completes typing, add it to accumulated text
    if (streamingData?.type === 'translation_chunk' && streamingData.text) {
      setAccumulatedText(prev => prev + (prev ? '\n\n' : '') + streamingData.text);
    }
  };

  const handleProceedToFeatures = () => {
    navigate('/features');
  };

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
  }

  const goToPrevPage = () => {
    setPageNumber(page => Math.max(page - 1, 1));
  };

  const goToNextPage = () => {
    setPageNumber(page => Math.min(page + 1, numPages || 1));
  };

  const zoomIn = () => {
    setScale(scale => Math.min(scale + 0.2, 2.0));
  };

  const zoomOut = () => {
    setScale(scale => Math.max(scale - 0.2, 0.6));
  };

  if (!uploadedFile) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <Header title="Translation Viewer" showBackButton backTo="/">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            translatedFileRef.current = null; // Reset to allow re-translation
            handleTranslation();
          }}
          disabled={isTranslating}
          className="border-slate-600 hover:border-cyan-400"
        >
          <Globe className="h-4 w-4 mr-2" />
          Translate Again
        </Button>
        <Button
          onClick={handleProceedToFeatures}
          disabled={isTranslating || !translatedContent}
          className="bg-gradient-to-r from-cyan-500 to-emerald-500 shadow-lg hover:scale-105 hover:shadow-2xl active:scale-95 transition-all duration-200 border-0 text-white font-semibold"
        >
          <ArrowRight className="h-4 w-4 mr-2" />
          Proceed to Feature Extraction
        </Button>
      </Header>

      {/* Content */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Original PDF Panel */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-6 animate-slide-in-right">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-white">Original Document</h2>
                <div className="text-sm text-slate-400">
                  {uploadedFile.name}
                </div>
              </div>
              
              {/* PDF Controls */}
              <div className="flex items-center gap-2 py-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={pageNumber <= 1}
                  className="border-slate-600"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                
                <span className="text-slate-400 text-sm px-2">
                  {pageNumber} of {numPages || 0}
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={pageNumber >= (numPages || 1)}
                  className="border-slate-600"
                >
                  <ArrowRight className="h-4 w-4" />
                </Button>
                
                <div className="ml-4 flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={zoomOut}
                    className="border-slate-600"
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  
                  <span className="text-slate-400 text-sm px-2">
                    {Math.round(scale * 100)}%
                  </span>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={zoomIn}
                    className="border-slate-600"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700 h-[calc(100vh-280px)] overflow-auto">
                <div className="flex justify-center">
                  <Document
                    file={uploadedFile}
                    onLoadSuccess={onDocumentLoadSuccess}
                    loading={
                      <div className="flex items-center justify-center p-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-2 border-cyan-400 border-t-transparent"></div>
                      </div>
                    }
                    error={
                      <div className="text-center p-8 text-slate-400">
                        <p>Failed to load PDF</p>
                        <p className="text-sm">Please try uploading again</p>
                      </div>
                    }
                  >
                    <Page
                      pageNumber={pageNumber}
                      scale={scale}
                      className="border border-slate-600 rounded"
                    />
                  </Document>
                </div>
              </div>
            </div>
          </Card>

          {/* Translated Panel */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-6 animate-slide-in-left">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-white">Translated Document</h2>
                <div className="flex items-center gap-3">
                  {isTranslating && (
                    <div className="flex items-center gap-2 text-cyan-400">
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-cyan-400 border-t-transparent"></div>
                      <span className="text-sm">Translating...</span>
                    </div>
                  )}
                  {translatedContent && !isTranslating && (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(translatedContent);
                          toast({
                            title: "Copied to Clipboard",
                            description: "Translated content has been copied to your clipboard.",
                          });
                        }}
                        className="border-slate-600 hover:border-cyan-400"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const blob = new Blob([translatedContent], { type: 'text/html' });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `${uploadedFile?.name.replace('.pdf', '_translated.html')}`;
                          document.body.appendChild(a);
                          a.click();
                          document.body.removeChild(a);
                          URL.revokeObjectURL(url);
                          toast({
                            title: "Download Complete",
                            description: "Translated HTML file has been downloaded.",
                          });
                        }}
                        className="border-slate-600 hover:border-cyan-400"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Export HTML
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="bg-slate-900/50 rounded-lg p-8 border border-slate-700 h-[calc(100vh-280px)] overflow-auto">
                {isStreaming ? (
                  <StreamingTranslationViewer
                    isStreaming={isStreaming}
                    streamingData={streamingData}
                    accumulatedText={accumulatedText}
                    onStreamingComplete={handleStreamingComplete}
                  />
                ) : isTranslating ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center space-y-6">
                      <div className="relative">
                        <Sparkles className="h-12 w-12 text-cyan-400 mx-auto animate-pulse" />
                        <div className="absolute -inset-3 bg-cyan-400/20 rounded-full blur-lg animate-pulse"></div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-cyan-400 font-medium">AI Translation in Progress</p>
                        <p className="text-slate-400 text-sm">Analyzing and translating your document...</p>
                      </div>
                      <div className="w-48 bg-slate-700 rounded-full h-2 mx-auto overflow-hidden">
                        <div className="bg-gradient-to-r from-cyan-500 to-emerald-500 h-full rounded-full animate-pulse w-3/4"></div>
                      </div>
                    </div>
                  </div>
                ) : translatedContent ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-emerald-400 mb-4">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <span className="text-sm font-medium">Translation Complete</span>
                    </div>
                    {translatedContent.trim() ? (
                      <div 
                        className="bg-white text-black rounded p-4 overflow-auto shadow-inner"
                        style={{ 
                          minHeight: '400px',
                          maxHeight: 'calc(100vh - 400px)',
                          fontFamily: 'Arial, sans-serif',
                          fontSize: '14px',
                          lineHeight: '1.6',
                          border: '1px solid #e5e7eb'
                        }}
                        dangerouslySetInnerHTML={{ 
                          __html: `
                            <style>
                              body { 
                                margin: 0; 
                                padding: 0; 
                                font-family: Arial, sans-serif; 
                                background: white;
                                color: #374151;
                              }
                              h1, h2, h3, h4, h5, h6 { 
                                color: #1f2937; 
                                margin-top: 1.5em; 
                                margin-bottom: 0.5em; 
                                font-weight: 600;
                              }
                              h1 { font-size: 1.5em; }
                              h2 { font-size: 1.3em; }
                              h3 { font-size: 1.1em; }
                              p { 
                                margin-bottom: 1em; 
                                color: #374151; 
                                line-height: 1.6;
                              }
                              ul, ol { 
                                margin-bottom: 1em; 
                                padding-left: 2em; 
                              }
                              li { 
                                margin-bottom: 0.5em; 
                                line-height: 1.5;
                              }
                              table { 
                                border-collapse: collapse; 
                                width: 100%; 
                                margin-bottom: 1em; 
                                font-size: 0.9em;
                              }
                              th, td { 
                                border: 1px solid #d1d5db; 
                                padding: 8px; 
                                text-align: left; 
                              }
                              th { 
                                background-color: #f3f4f6; 
                                font-weight: bold; 
                                color: #1f2937;
                              }
                              .highlight { 
                                background-color: #fef3c7; 
                                padding: 2px 4px; 
                                border-radius: 3px; 
                              }
                              blockquote {
                                border-left: 4px solid #3b82f6;
                                margin: 1em 0;
                                padding-left: 1em;
                                font-style: italic;
                                color: #6b7280;
                              }
                              code {
                                background-color: #f3f4f6;
                                padding: 2px 4px;
                                border-radius: 3px;
                                font-family: 'Courier New', monospace;
                                font-size: 0.9em;
                              }
                              pre {
                                background-color: #f3f4f6;
                                padding: 1em;
                                border-radius: 6px;
                                overflow-x: auto;
                                margin: 1em 0;
                              }
                              pre code {
                                background: none;
                                padding: 0;
                              }
                            </style>
                            ${translatedContent}
                          `
                        }}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full text-slate-400">
                        <p>No translated content available</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-slate-400">
                    <p>Translated content will appear here</p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TranslationViewer;
